/**
 * Test file for embed link validation improvements
 * Tests the enhanced validation patterns and multiple platform support
 */

import { isValidVideoLink, parseVideoLinks, extractVideoUrl, isSecureVideoLink } from '../utils/videoSecurity';

// Test data with various embed link formats from different platforms
const testEmbedLinks = {
  // YouTube formats
  youtube: [
    'https://www.youtube.com/embed/dQw4w9WgXcQ',
    'https://youtube-nocookie.com/embed/dQw4w9WgXcQ',
    'https://youtu.be/dQw4w9WgXcQ',
    '//www.youtube.com/embed/dQw4w9WgXcQ',
  ],
  
  // Vimeo formats (corrected patterns)
  vimeo: [
    'https://player.vimeo.com/video/123456789',
    '//player.vimeo.com/video/123456789',
    'https://vimeo.com/123456789',
    '<iframe src="https://player.vimeo.com/video/123456789" allowfullscreen></iframe>',
  ],
  
  // Dailymotion formats
  dailymotion: [
    'https://www.dailymotion.com/embed/video/x123456',
    '//www.dailymotion.com/embed/video/x123456',
    'https://dai.ly/x123456',
  ],
  
  // Twitch formats
  twitch: [
    'https://player.twitch.tv/embed/video/123456789',
    'https://clips.twitch.tv/embed?clip=ClipName',
    '//player.twitch.tv/embed/video/123456789',
  ],
  
  // Other platforms
  other: [
    'https://streamable.com/e/abc123',
    'https://streamable.com/abc123',
    'https://fast.wistia.net/embed/iframe/abc123',
    'https://content.jwplatform.com/players/abc123-def456.js',
    'https://www.bitchute.com/embed/abc123/',
    'https://rumble.com/embed/abc123/',
    'https://odysee.com/$/embed/video-name/abc123',
  ],

  // 2embed.cc formats
  '2embed': [
    'https://www.2embed.cc/embed/574475',
    'https://2embed.cc/embed/574475',
    'https://www.2embed.cc/movie/574475',  // User's format
    'https://2embed.cc/movie/574475',
    'https://www.2embed.to/embed/574475',
    'https://2embed.to/movie/574475',
    '//www.2embed.cc/embed/574475',
    '//2embed.cc/movie/574475',
  ],
  
  // Generic embed patterns
  generic: [
    'https://example.com/embed/abc123',
    '//example.com/player/def456',
    'https://custom-player.com/embed/xyz789',
  ],
  
  // Iframe formats
  iframes: [
    '<iframe src="https://player.example.com/embed/abc123" width="560" height="315" allowfullscreen></iframe>',
    '<iframe src="//player.example.com/embed/abc123" allowfullscreen />',
    '<iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
  ],
  
  // Invalid formats (should fail validation)
  invalid: [
    'not-a-link',
    'https://example.com/regular-page',
    'javascript:alert("xss")',
    'data:text/html,<script>alert("xss")</script>',
    '<script>alert("xss")</script>',
    '',
    null,
    undefined,
  ],
};

/**
 * Test embed link validation for all platforms
 */
export function testEmbedLinkValidation(): boolean {
  console.group('🔗 Embed Link Validation Tests');
  
  let totalTests = 0;
  let passedTests = 0;
  
  // Test valid links
  Object.entries(testEmbedLinks).forEach(([platform, links]) => {
    if (platform === 'invalid') return; // Skip invalid links for now
    
    console.group(`Testing ${platform} links`);
    
    links.forEach((link, index) => {
      totalTests++;
      const isValid = isValidVideoLink(link);
      const isSecure = isSecureVideoLink(link);
      
      if (isValid && isSecure) {
        passedTests++;
        console.log(`✓ ${platform}[${index}]: PASS - ${link.substring(0, 50)}...`);
      } else {
        console.error(`✗ ${platform}[${index}]: FAIL - ${link.substring(0, 50)}...`);
        console.error(`  Valid: ${isValid}, Secure: ${isSecure}`);
      }
    });
    
    console.groupEnd();
  });
  
  // Test invalid links (should fail)
  console.group('Testing invalid links (should fail)');
  testEmbedLinks.invalid.forEach((link, index) => {
    totalTests++;
    const isValid = isValidVideoLink(link as string);
    
    if (!isValid) {
      passedTests++;
      console.log(`✓ Invalid[${index}]: PASS - Correctly rejected: ${link}`);
    } else {
      console.error(`✗ Invalid[${index}]: FAIL - Should have been rejected: ${link}`);
    }
  });
  console.groupEnd();
  
  const successRate = (passedTests / totalTests) * 100;
  console.log(`\n📊 Results: ${passedTests}/${totalTests} tests passed (${successRate.toFixed(1)}%)`);
  
  console.groupEnd();
  
  return successRate >= 90; // 90% success rate threshold
}

/**
 * Test multiple embed links parsing
 */
export function testMultipleEmbedLinks(): boolean {
  console.group('🔗 Multiple Embed Links Tests');
  
  const multipleLinksTest = `https://www.youtube.com/embed/dQw4w9WgXcQ
https://player.vimeo.com/video/123456789
//player.example.com/embed/abc123
<iframe src="https://streamable.com/e/def456" allowfullscreen></iframe>`;
  
  const parsed = parseVideoLinks(multipleLinksTest);
  const validCount = parsed.filter(link => isValidVideoLink(link)).length;
  
  console.log(`Parsed ${parsed.length} links, ${validCount} valid`);
  parsed.forEach((link, index) => {
    const isValid = isValidVideoLink(link);
    console.log(`${isValid ? '✓' : '✗'} Link ${index + 1}: ${link.substring(0, 50)}...`);
  });
  
  const success = parsed.length === 4 && validCount >= 3;
  console.log(`Result: ${success ? 'PASS' : 'FAIL'}`);
  
  console.groupEnd();
  
  return success;
}

/**
 * Test URL extraction from iframes
 */
export function testUrlExtraction(): boolean {
  console.group('🔗 URL Extraction Tests');
  
  const testCases = [
    {
      input: '<iframe src="https://player.example.com/embed/abc123" allowfullscreen></iframe>',
      expected: 'https://player.example.com/embed/abc123'
    },
    {
      input: '//player.example.com/embed/def456',
      expected: 'https://player.example.com/embed/def456'
    },
    {
      input: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
      expected: 'https://www.youtube.com/embed/dQw4w9WgXcQ'
    }
  ];
  
  let passed = 0;
  
  testCases.forEach((testCase, index) => {
    const result = extractVideoUrl(testCase.input);
    const success = result === testCase.expected;
    
    if (success) {
      passed++;
      console.log(`✓ Test ${index + 1}: PASS`);
    } else {
      console.error(`✗ Test ${index + 1}: FAIL`);
      console.error(`  Expected: ${testCase.expected}`);
      console.error(`  Got: ${result}`);
    }
  });
  
  const success = passed === testCases.length;
  console.log(`Result: ${success ? 'PASS' : 'FAIL'} (${passed}/${testCases.length})`);
  
  console.groupEnd();
  
  return success;
}

/**
 * Run all embed link tests
 */
export function runAllEmbedLinkTests(): boolean {
  console.group('🧪 Running All Embed Link Tests');
  
  const results = [
    testEmbedLinkValidation(),
    testMultipleEmbedLinks(),
    testUrlExtraction()
  ];
  
  const allPassed = results.every(Boolean);
  const passedCount = results.filter(Boolean).length;
  
  console.log(`\n🎯 Overall Result: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  console.log(`📊 Summary: ${passedCount}/${results.length} test suites passed`);
  
  console.groupEnd();
  
  return allPassed;
}

// Export test data for use in other components
export { testEmbedLinks };

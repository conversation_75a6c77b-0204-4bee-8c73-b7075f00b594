/**
 * Video Link Security Utilities
 * 
 * This module provides client-side security for video embed links to prevent
 * easy extraction via browser developer tools. While not foolproof against
 * determined attackers, it significantly raises the barrier for casual users.
 * 
 * Future Enhancement: Replace with server-side proxy when backend is implemented.
 */

// Simple encoding key - in production, this should be environment-specific
const ENCODING_KEY = 'StreamDB_2024_Security_Key_v1';

/**
 * Simple XOR-based encoding for video links
 * This is obfuscation, not true encryption, but prevents casual inspection
 */
function xorEncode(text: string, key: string): string {
  let result = '';
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    result += String.fromCharCode(charCode);
  }
  return result;
}

/**
 * Base64 encode with URL-safe characters
 */
function base64UrlEncode(str: string): string {
  return btoa(str)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Base64 decode with URL-safe characters
 */
function base64UrlDecode(str: string): string {
  // Add padding if needed
  const padding = '='.repeat((4 - str.length % 4) % 4);
  const base64 = str.replace(/-/g, '+').replace(/_/g, '/') + padding;
  return atob(base64);
}

/**
 * Encode video links for secure storage
 * @param videoLinks - Raw video links (one per line)
 * @returns Encoded string safe for client-side storage
 */
export function encodeVideoLinks(videoLinks: string): string {
  if (!videoLinks || videoLinks.trim() === '') {
    return '';
  }

  try {
    // Add timestamp and random salt for additional obfuscation
    const timestamp = Date.now().toString();
    const salt = Math.random().toString(36).substring(2, 15);
    const payload = JSON.stringify({
      links: videoLinks,
      timestamp,
      salt
    });

    // XOR encode the payload
    const encoded = xorEncode(payload, ENCODING_KEY);
    
    // Base64 encode for safe storage
    return base64UrlEncode(encoded);
  } catch (error) {
    console.error('Error encoding video links:', error);
    return '';
  }
}

/**
 * Decode video links for playback
 * @param encodedLinks - Encoded video links string
 * @returns Decoded video links or empty string if invalid
 */
export function decodeVideoLinks(encodedLinks: string): string {
  if (!encodedLinks || encodedLinks.trim() === '') {
    return '';
  }

  try {
    // Base64 decode
    const decoded = base64UrlDecode(encodedLinks);
    
    // XOR decode
    const payload = xorEncode(decoded, ENCODING_KEY);
    
    // Parse JSON
    const data = JSON.parse(payload);
    
    // Validate structure
    if (data && typeof data.links === 'string') {
      return data.links;
    }
    
    return '';
  } catch (error) {
    console.error('Error decoding video links:', error);
    return '';
  }
}

/**
 * Parse video links into an array of individual links
 * @param videoLinks - Raw or decoded video links string
 * @returns Array of individual video links
 */
export function parseVideoLinks(videoLinks: string): string[] {
  if (!videoLinks || videoLinks.trim() === '') {
    return [];
  }

  return videoLinks
    .split('\n')
    .map(link => link.trim())
    .filter(link => link.length > 0);
}

/**
 * Generate player names for multiple video links
 * @param links - Array of video links
 * @returns Array of player names (Player 1, Player 2, etc.)
 */
export function generatePlayerNames(links: string[]): string[] {
  return links.map((_, index) => `Player ${index + 1}`);
}

/**
 * Validate if a string appears to be a valid video embed link
 * @param link - Video link to validate
 * @returns True if link appears valid
 */
export function isValidVideoLink(link: string): boolean {
  if (!link || typeof link !== 'string') {
    return false;
  }

  const trimmedLink = link.trim();

  // Check for common video embed patterns
  const patterns = [
    // Generic embed patterns
    /^https?:\/\/.*\/embed\//i,
    /^\/\/.*\/embed\//i,
    /^https?:\/\/.*\/player\//i,
    /^\/\/.*\/player\//i,

    // Iframe patterns (more flexible)
    /^<iframe[^>]*src=["'][^"']+["'][^>]*>.*<\/iframe>/i,
    /^<iframe[^>]*src=["'][^"']+["'][^>]*\/?>$/i,

    // YouTube patterns
    /youtube\.com\/embed\//i,
    /youtu\.be\//i,
    /youtube-nocookie\.com\/embed\//i,

    // Vimeo patterns (corrected)
    /player\.vimeo\.com\/video\//i,
    /vimeo\.com\/\d+/i,

    // Dailymotion patterns
    /dailymotion\.com\/embed\//i,
    /dai\.ly\//i,

    // Twitch patterns
    /player\.twitch\.tv/i,
    /clips\.twitch\.tv/i,

    // Other popular platforms
    /streamable\.com\/e\//i,
    /streamable\.com\/\w+/i,
    /wistia\.com\/embed\//i,
    /fast\.wistia\.net\/embed\//i,
    /jwplatform\.com\/players\//i,
    /content\.jwplatform\.com\/players\//i,
    /bitchute\.com\/embed\//i,
    /rumble\.com\/embed\//i,
    /odysee\.com\/\$\/embed\//i,

    // 2embed.cc patterns
    /2embed\.cc\/embed\//i,
    /2embed\.cc\/movie\//i,  // Support both embed and movie URLs
    /2embed\.to\/embed\//i,  // Alternative domain
    /2embed\.to\/movie\//i,

    // Generic streaming patterns
    /\/embed\/[a-zA-Z0-9_-]+/i,
    /\/player\/[a-zA-Z0-9_-]+/i,
    /\/movie\/[a-zA-Z0-9_-]+/i,  // For movie hosting services like 2embed.cc
    /\/video\/[a-zA-Z0-9_-]+/i,  // For video hosting services
    /\/watch\?v=/i,

    // Protocol-relative URLs for common patterns
    /^\/\/[^\/]+\/(embed|player|movie|video)\//i
  ];

  return patterns.some(pattern => pattern.test(trimmedLink));
}

/**
 * Extract iframe src from embed code if it's an iframe
 * @param embedCode - Embed code (could be iframe or direct URL)
 * @returns Clean URL or original code
 */
export function extractVideoUrl(embedCode: string): string {
  if (!embedCode) return '';

  const trimmed = embedCode.trim();

  // If it's an iframe, extract the src
  const iframeMatch = trimmed.match(/src=["']([^"']+)["']/i);
  if (iframeMatch) {
    let url = iframeMatch[1];

    // Ensure protocol-relative URLs have https
    if (url.startsWith('//')) {
      url = 'https:' + url;
    }

    return url;
  }

  // If it's already a URL, normalize it
  let url = trimmed;

  // Ensure protocol-relative URLs have https
  if (url.startsWith('//')) {
    url = 'https:' + url;
  }

  // Transform 2embed.cc movie URLs to embed URLs for better compatibility
  if (url.includes('2embed.cc/movie/') || url.includes('2embed.to/movie/')) {
    url = url.replace('/movie/', '/embed/');
    console.log('Transformed 2embed.cc movie URL to embed URL:', url);
  }

  // If it's a relative URL without protocol, assume https
  if (url.startsWith('/') && !url.startsWith('//')) {
    // This shouldn't happen for embed URLs, but handle gracefully
    console.warn('Relative URL detected in embed code:', url);
  }

  return url;
}

/**
 * Security check: Prevent common XSS patterns in video links
 * @param link - Video link to check
 * @returns True if link appears safe
 */
export function isSecureVideoLink(link: string): boolean {
  if (!link) return false;

  const dangerous = [
    'javascript:',
    'data:',
    'vbscript:',
    '<script',
    'onload=',
    'onerror=',
    'onclick='
  ];

  const lowerLink = link.toLowerCase();
  return !dangerous.some(pattern => lowerLink.includes(pattern));
}

/**
 * Detect video platform from URL
 * @param url - Video URL to analyze
 * @returns Platform identifier or 'unknown'
 */
export function detectVideoPlatform(url: string): string {
  if (!url) return 'unknown';

  const lowerUrl = url.toLowerCase();

  // YouTube variants
  if (lowerUrl.includes('youtube.com') || lowerUrl.includes('youtu.be') || lowerUrl.includes('youtube-nocookie.com')) {
    return 'youtube';
  }

  // Vimeo
  if (lowerUrl.includes('vimeo.com')) {
    return 'vimeo';
  }

  // Dailymotion
  if (lowerUrl.includes('dailymotion.com') || lowerUrl.includes('dai.ly')) {
    return 'dailymotion';
  }

  // Twitch
  if (lowerUrl.includes('twitch.tv')) {
    return 'twitch';
  }

  // Wistia
  if (lowerUrl.includes('wistia.com') || lowerUrl.includes('wistia.net')) {
    return 'wistia';
  }

  // JW Player
  if (lowerUrl.includes('jwplatform.com') || lowerUrl.includes('jwplayer.com')) {
    return 'jwplayer';
  }

  // Streamable
  if (lowerUrl.includes('streamable.com')) {
    return 'streamable';
  }

  // 2embed services
  if (lowerUrl.includes('2embed.cc') || lowerUrl.includes('2embed.to')) {
    return '2embed';
  }

  // BitChute
  if (lowerUrl.includes('bitchute.com')) {
    return 'bitchute';
  }

  // Rumble
  if (lowerUrl.includes('rumble.com')) {
    return 'rumble';
  }

  // Odysee
  if (lowerUrl.includes('odysee.com')) {
    return 'odysee';
  }

  return 'unknown';
}
